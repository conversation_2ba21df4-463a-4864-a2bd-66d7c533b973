using System.Collections.Concurrent;
using Gsdt.ModbusGateway.Models;

namespace Gsdt.ModbusGateway.Tests.Services;

/// <summary>
/// 模拟的ModbusClient，用于测试线程安全性
/// </summary>
public class MockModbusClient : ModbusClient
{
    private readonly object _lock = new();
    private readonly Random _random = new();

    // 响应数据
    private byte[] _readCoilsResponse = Array.Empty<byte>();
    private byte[] _readDiscreteInputsResponse = Array.Empty<byte>();
    private byte[] _readHoldingRegistersResponse = Array.Empty<byte>();
    private byte[] _readInputRegistersResponse = Array.Empty<byte>();

    // 操作延迟
    private TimeSpan _operationDelay = TimeSpan.Zero;

    // 异常设置
    private bool _shouldThrowException = false;
    private double _exceptionProbability = 0.0;

    // 调用计数器（线程安全）
    private int _readCoilsCallCount = 0;
    private int _readDiscreteInputsCallCount = 0;
    private int _readHoldingRegistersCallCount = 0;
    private int _readInputRegistersCallCount = 0;
    private int _writeSingleCoilCallCount = 0;
    private int _writeSingleRegisterCallCount = 0;
    private int _writeMultipleCoilsCallCount = 0;
    private int _writeMultipleRegistersCallCount = 0;

    // 最后调用的参数
    public byte LastSlaveId { get; private set; }
    public ushort LastStartAddress { get; private set; }
    public ushort LastAddress { get; private set; }
    public ushort LastQuantity { get; private set; }
    public bool LastBoolValue { get; private set; }
    public ushort LastUshortValue { get; private set; }
    public bool[] LastBoolArrayValue { get; private set; } = Array.Empty<bool>();
    public ushort[] LastUshortArrayValue { get; private set; } = Array.Empty<ushort>();

    // 调用状态
    public bool ReadCoilsCalled => _readCoilsCallCount > 0;
    public bool ReadDiscreteInputsCalled => _readDiscreteInputsCallCount > 0;
    public bool ReadHoldingRegistersCalled => _readHoldingRegistersCallCount > 0;
    public bool ReadInputRegistersCalled => _readInputRegistersCallCount > 0;
    public bool WriteSingleCoilCalled => _writeSingleCoilCallCount > 0;
    public bool WriteSingleRegisterCalled => _writeSingleRegisterCallCount > 0;
    public bool WriteMultipleCoilsCalled => _writeMultipleCoilsCallCount > 0;
    public bool WriteMultipleRegistersCalled => _writeMultipleRegistersCallCount > 0;

    // 调用次数
    public int ReadCoilsCallCount => _readCoilsCallCount;
    public int ReadDiscreteInputsCallCount => _readDiscreteInputsCallCount;
    public int ReadHoldingRegistersCallCount => _readHoldingRegistersCallCount;
    public int ReadInputRegistersCallCount => _readInputRegistersCallCount;
    public int WriteSingleCoilCallCount => _writeSingleCoilCallCount;
    public int WriteSingleRegisterCallCount => _writeSingleRegisterCallCount;
    public int WriteMultipleCoilsCallCount => _writeMultipleCoilsCallCount;
    public int WriteMultipleRegistersCallCount => _writeMultipleRegistersCallCount;

    // 设置方法
    public void SetReadCoilsResponse(byte[] response) => _readCoilsResponse = response;
    public void SetReadDiscreteInputsResponse(byte[] response) => _readDiscreteInputsResponse = response;
    public void SetReadHoldingRegistersResponse(byte[] response) => _readHoldingRegistersResponse = response;
    public void SetReadInputRegistersResponse(byte[] response) => _readInputRegistersResponse = response;
    public void SetOperationDelay(TimeSpan delay) => _operationDelay = delay;
    public void SetShouldThrowException(bool shouldThrow) => _shouldThrowException = shouldThrow;
    public void SetExceptionProbability(double probability) => _exceptionProbability = Math.Clamp(probability, 0.0, 1.0);

    // 重置方法
    public void Reset()
    {
        lock (_lock)
        {
            _readCoilsCallCount = 0;
            _readDiscreteInputsCallCount = 0;
            _readHoldingRegistersCallCount = 0;
            _readInputRegistersCallCount = 0;
            _writeSingleCoilCallCount = 0;
            _writeSingleRegisterCallCount = 0;
            _writeMultipleCoilsCallCount = 0;
            _writeMultipleRegistersCallCount = 0;
        }
    }

    // 模拟操作延迟和异常
    private async Task SimulateOperationAsync(CancellationToken cancellationToken = default)
    {
        if (_operationDelay > TimeSpan.Zero)
        {
            await Task.Delay(_operationDelay, cancellationToken);
        }

        if (_shouldThrowException && _random.NextDouble() < _exceptionProbability)
        {
            throw new InvalidOperationException("模拟的Modbus操作异常");
        }
    }

    // 实现ModbusClient的抽象方法
    public override async Task<Memory<byte>> ReadCoilsAsync(byte slaveId, ushort startAddress, ushort quantity, CancellationToken cancellationToken)
    {
        await SimulateOperationAsync(cancellationToken);

        lock (_lock)
        {
            Interlocked.Increment(ref _readCoilsCallCount);
            LastSlaveId = slaveId;
            LastStartAddress = startAddress;
            LastQuantity = quantity;
        }

        return new Memory<byte>(_readCoilsResponse);
    }

    public override async Task<Memory<byte>> ReadDiscreteInputsAsync(byte slaveId, ushort startAddress, ushort quantity, CancellationToken cancellationToken)
    {
        await SimulateOperationAsync(cancellationToken);

        lock (_lock)
        {
            Interlocked.Increment(ref _readDiscreteInputsCallCount);
            LastSlaveId = slaveId;
            LastStartAddress = startAddress;
            LastQuantity = quantity;
        }

        return new Memory<byte>(_readDiscreteInputsResponse);
    }

    public override async Task<Memory<byte>> ReadHoldingRegistersAsync(byte slaveId, ushort startAddress, ushort quantity, CancellationToken cancellationToken)
    {
        await SimulateOperationAsync(cancellationToken);

        lock (_lock)
        {
            Interlocked.Increment(ref _readHoldingRegistersCallCount);
            LastSlaveId = slaveId;
            LastStartAddress = startAddress;
            LastQuantity = quantity;
        }

        return new Memory<byte>(_readHoldingRegistersResponse);
    }

    public override async Task<Memory<byte>> ReadInputRegistersAsync(byte slaveId, ushort startAddress, ushort quantity, CancellationToken cancellationToken)
    {
        await SimulateOperationAsync(cancellationToken);

        lock (_lock)
        {
            Interlocked.Increment(ref _readInputRegistersCallCount);
            LastSlaveId = slaveId;
            LastStartAddress = startAddress;
            LastQuantity = quantity;
        }

        return new Memory<byte>(_readInputRegistersResponse);
    }

    public override async Task WriteSingleCoilAsync(byte slaveId, ushort address, bool value, CancellationToken cancellationToken)
    {
        await SimulateOperationAsync(cancellationToken);

        lock (_lock)
        {
            Interlocked.Increment(ref _writeSingleCoilCallCount);
            LastSlaveId = slaveId;
            LastAddress = address;
            LastBoolValue = value;
        }
    }

    public override async Task WriteSingleRegisterAsync(byte slaveId, ushort address, ushort value, CancellationToken cancellationToken)
    {
        await SimulateOperationAsync(cancellationToken);

        lock (_lock)
        {
            Interlocked.Increment(ref _writeSingleRegisterCallCount);
            LastSlaveId = slaveId;
            LastAddress = address;
            LastUshortValue = value;
        }
    }

    public override async Task WriteMultipleCoilsAsync(byte slaveId, ushort startAddress, bool[] values, CancellationToken cancellationToken)
    {
        await SimulateOperationAsync(cancellationToken);

        lock (_lock)
        {
            Interlocked.Increment(ref _writeMultipleCoilsCallCount);
            LastSlaveId = slaveId;
            LastStartAddress = startAddress;
            LastBoolArrayValue = values;
        }
    }

    public override async Task WriteMultipleRegistersAsync(byte slaveId, ushort startAddress, ushort[] values, CancellationToken cancellationToken)
    {
        await SimulateOperationAsync(cancellationToken);

        lock (_lock)
        {
            Interlocked.Increment(ref _writeMultipleRegistersCallCount);
            LastSlaveId = slaveId;
            LastStartAddress = startAddress;
            LastUshortArrayValue = values;
        }
    }

    // 连接相关的属性和方法（简单实现）
    public bool IsConnected { get; set; } = true;

    public void Connect(object connectionInfo)
    {
        // 模拟连接，不做任何实际操作
        IsConnected = true;
    }

    public void Disconnect()
    {
        // 模拟断开连接，不做任何实际操作
        IsConnected = false;
    }

    public override void Dispose()
    {
        // 模拟资源释放，不做任何实际操作
        IsConnected = false;
    }
}
